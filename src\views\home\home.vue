<template>
  <div class="app">
    <div id="amapcontainer" style="width: 100vw; height: 100vh"></div>

  </div>
</template>

<script>
import AMapLoader from "@amap/amap-jsapi-loader";

export default {
  data() {
    return {
      timer: "", // 定义一个定时器
      timeNum: "--:--",
    };
  },
  methods: {
    toDataList(e) {
      console.log(e);
      if (e == "1") {
        this.$router.push({ path: "/productList" });
        return false;
      } else if (e == "2") {
        this.$router.push({ path: "/caseList" });
        return false;
      } else if (e == "3") {
        this.$router.push({ path: "/serverList" });
        return false;
      } else if (e == "4") {
        // this.$router.push({ path: "/map" });
        this.$router.push({
          path: "/constructionList",
          query: { activeId: 1 },
        });
        return false;
      }
    },

    //跳转左侧导航栏
    toLeftBtn(e) {
      if (e == "1") {
        this.$router.push({
          path: "/constructionList",
          query: { activeId: "2" },
        });
        return false;
      } else if (e == "2") {
        this.$router.push({
          path: "/constructionList",
          query: { activeId: "2" },
        });
        return false;
      }
    },

    initAMap() {
      AMapLoader.reset();
      AMapLoader.load({
        key: "e18de717bc0db5e1faa219d669d00e05", // 申请好的Web端开发者Key，首次调用 load 时必填
        // version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: [
          "AMap.Scale",
          "AMap.ToolBar",
          "AMap.ControlBar",
          "AMap.Geocoder",
          "AMap.Marker",
          "AMap.CitySearch",
          "AMap.Geolocation",
          "AMap.AutoComplete",
          "AMap.InfoWindow",
        ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      })
        .then((AMap) => {
          // 获取到作为地图容器的DOM元素，创建地图实例
          this.map = new AMap.Map("amapcontainer", {
            //设置地图容器id
            // resizeEnable: true,
            zoom: "8", // 地图显示的缩放级别
            viewMode: "3D", // 使用3D视图
            zoomEnable: true, // 地图是否可缩放，默认值为true
            dragEnable: true, // 地图是否可通过鼠标拖拽平移，默认为true
            doubleClickZoom: true, // 地图是否可通过双击鼠标放大地图，默认为true
            // zoom: this.zoom, //初始化地图级别
            // center: [113.370824, 23.131265], // 初始化中心点坐标 广州
            // mapStyle: "amap://styles/darkblue", // 设置颜色底层
            content: '<div style="width:200px;">这里是点位信息</div>',
          });
          // 在图面添加工具条控件, 工具条控件只有缩放功能
          this.map.addControl(
            new AMap.ToolBar({
              liteStyle: true,
            })
          );
          //获取并展示当前城市信息

          this.setMapMarker();
        })
        .catch((e) => {
          console.log(e);
        });
    },

    getTime() {
      this.timer = setInterval(() => {
        // 获取当前时间的各个部分
        let timeDate = new Date();
        let hours = timeDate.getHours();
        // 格式化小时
        hours = hours >= 10 ? hours : "0" + hours;
        let minutes = timeDate.getMinutes();
        // 格式化分钟
        minutes = minutes >= 10 ? minutes : "0" + minutes;

        // 将获取的时间信息赋值给nowTime
        this.timeNum = `${hours}:${minutes}`;
      }, 1000); // 每隔一秒更新时间
    },
  },
  created() {
    this.getTime();
    this.initAMap;
  },
  //beforeDestroy是Vue组件的生命周期钩子之一，在组件销毁之前调用。在这里，它清除了之前设定的定时器，以避免内存泄漏。
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
};
</script>

<style lang="scss" scoped>
.app {
  width: 100vw;
  height: 100vh;
  background: url("../../assets/images/home_bg.png");
  background-size: cover; /* 覆盖整个元素 */
  background-repeat: no-repeat; /* 不重复图片 */
  background-position: center; /* 居中图片 */
  overflow: hidden;
  display: flex;
  .left_box {
    width: 1.75rem;
    // width: 12.5vw;
    height: 100vh;
    background-color: white;
    padding: 30px 20px;
    box-sizing: border-box;
    border-radius: 0 32px 32px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .logo_box {
      width: 1.125rem;
      height: 1.125rem;
      margin-bottom: 0.0625rem;
      .logo_img {
        width: 1.125rem;
        height: 1.125rem;
      }
    }
    .fun_box {
      // width: 110px;
      // height: 110px;
      // background: pink;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-bottom: 50px;
      cursor: pointer;
      .icon_img1 {
        width: 0.375rem;
        // height: 2.625rem;
      }
      .fun_title {
        font-size: 0.1875rem;
        color: #393e3e;
        margin-top: 16px;
      }
    }
  }
  .right_box {
    width: 87.5vw;
    height: 100vh;
    padding: 14px 30px 14px 50px;
    box-sizing: border-box;
    .top_box {
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      .exit_img {
        width: 0.3125rem;
        height: 0.3125rem;
        .e_img {
          width: 0.3125rem;
          height: 0.3125rem;
        }
      }
      .title_text {
        display: inline-block;
        margin: 0 10px 0 20px;
        .p_title {
          font-size: 0.1875rem;
          flex-wrap: 600;
          color: black;
        }
      }
      .select_box {
        width: 0.3125rem;
        height: 0.3125rem;
        background: rgba(68, 68, 68, 0.15);
        border-radius: 46px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-right: 0.25rem;
        .s_img {
          width: 0.1875rem;
          height: 0.1875rem;
        }
      }
      .time_box {
        font-size: 0.1875rem;
        margin: 0 0.3125rem 0 0;
        position: relative;
        .time_m {
          color: #191a1c;
          font-weight: 400;
        }
        ::after {
          content: "";
          width: 0.0105rem;
          height: 0.1875rem;
          background: #191a1c;
          position: absolute;
          left: -0.1875rem;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }

    .title_box1 {
      width: 100%;
      .box_one {
        width: 100%;
        display: flex;
        align-items: center;
        margin-top: -0.0625rem;
        .icon7 {
          width: 1rem;
          height: 0.1563rem;
          margin-right: 0.0938rem;
          margin-top: 0.125rem;
        }
        .icon6 {
          width: 0.625rem;
          height: 0.625rem;
        }
      }
      .box_two {
        // font-size: 52px;
        font-weight: #313335;
        // margin-top: -0.0625rem;
        margin-bottom: 0.25rem;

        .text_img {
          width: 1.3125rem;
          height: 0.3rem;
        }
      }
    }
    .content_box {
      width: 100%;
      height: 100%;
      display: flex;

      .con_box1 {
        width: 3.25rem;
        .img_a {
          width: 100%;
          // height: 100%;
          cursor: pointer;
          .img_aImg {
            width: 100%;
            height: 100%;
          }
        }
      }
      .con_box2 {
        width: 3.75rem;
        height: 6.125rem;
        background: linear-gradient(#ffffff, #edf4ff);
        margin: 0 0.375rem 0 0.5625rem;
        border-radius: 0.5rem;
        box-shadow: 0px 39px 29.5px 0px #dde5eb;
        padding: 30px 20px 20px 20px;
        box-sizing: border-box;
        .title_a {
          color: #020202;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 0.25rem;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
        }
        .type_box1 {
          width: 100%;
          display: flex;
          justify-content: flex-end;
          cursor: pointer;
          .yuan_box {
            width: 1.75rem;
            height: 1.75rem;
            background: white;
            border-radius: 1.75rem;
            filter: drop-shadow(24px 42px 41.6px #dde5eb);
            margin: 0.25rem 0.125rem 0 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .img1 {
              width: 60%;
              // height: 6.875rem;
            }
            .p1 {
              font-size: 0.1875rem;
              margin-top: 0.0625rem;
            }
          }
        }
        .type_box2 {
          width: 100%;
          display: flex;
          cursor: pointer;
          .yuan_box {
            width: 1.5rem;
            height: 1.5rem;
            background: white;
            border-radius: 1.375rem;
            filter: drop-shadow(0.625rem 1rem 1.25rem #dde5eb);
            margin: 0.1875rem 0 0 0.1875rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .img1 {
              width: 0.625rem;
              height: 0.625rem;
            }
            .p1 {
              font-size: 0.1875rem;
              margin-top: 0.0625rem;
            }
          }
        }
      }
      .con_box3 {
        width: 3.75rem;
        height: 6.125rem;
        background: white;
        margin: 0 0.25rem 0 0.25rem;
        border-radius: 0.5rem;
        position: relative;
        overflow: hidden;
        .mask_box {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background: rgba(91, 148, 255, 0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          .img_dit {
            width: 2.25rem;
            height: 1.875rem;
          }
        }
      }
    }
  }
}
</style>