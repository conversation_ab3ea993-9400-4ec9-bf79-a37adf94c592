<template>
  <div class="app">
    <div class="top_box">
      <img @click="goBack" class="back_img" src="../../assets/images/back.png" alt="">
      <p class="top_title">案例列表</p>
    </div>
    <keep-alive>
      <div class="content_box">
        <div class="left_box">
          <ul class="ul">
            <li class="li" :class="item.id == styleActiveId ? 'active_left' : ''" v-for="(item,index) in styleData" :key="index" @click="styleBtn(item.id)">{{item.title}}</li>
          </ul>
        </div>
        <div class="right_box">
          <div class="right_top">
            <div class="top_left">
              <ul class="ul">
                <li class="li" :class="item.id == activeTypeId ? 'active_type' : ''" v-for="(item,index) in typeData" :key="index" @click="typeBtn(item.id)">{{item.title}}</li>
              </ul>
              <ul class="ul">
                <li class="li" :class="item.id == activeAreaId ? 'active_type' : ''" v-for="(item,index) in areaData" :key="index" @click="areaBtn(item.id)">{{item.title}}</li>
              </ul>
            </div>
            <div class="top_right">
              <div class="input_box">
                <input type="text" @input="inputValue" class="input" placeholder="搜索产品信息">
                <img class="select_img" src="../../assets/images/select.png" alt="">
              </div>
            </div>
          </div>

          <div class="product_list">
            <div class="pro_boxs" v-for="(item,index) in caseDataList" :key="index" @click="toDetails(item.vrAddress)">
              <div class="img_box">
                <img class="cp_img" :src="item.zhuye" alt="">
              </div>
              <div class="cp_name">{{item.name}}</div>
            </div>
          </div>

          <div class="fenye_box">
            <el-pagination background layout="prev, pager, next" :page-size="100" :total="totalNum">
            </el-pagination>
          </div>

        </div>
      </div>
    </keep-alive>
  </div>
</template>

<script>
export default {
  data() {
    return {
      userId: "1",
      curPage: "1",
      caseDataList: [], //案例数据
      totalNum: 0,
      styleActiveId: "", //风格选中id
      activeTypeId: "", //户型选中id
      activeAreaId: "", //户型选中id
      styleData: [], //风格
      typeData: [], //户型
      areaData: [], //面积
    };
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    //跳转详情
    toDetails(url) {
      // this.$router.push({ path: "/caseDetails" });
      // const url = 'https://mp.weixin.qq.com/s/your-wechat-link'; // 替换为你的公众号链接
      window.location.href = url;
    },

    //点击切换风格分类
    styleBtn(e) {
      this.styleActiveId = e;
      ((this.caseDataList = []), (this.curPage = 1)), this.getDataList();
    },
    //点击切换风格分类
    typeBtn(e) {
      this.curPage = 1;
      this.caseDataList = [];
      if (e == this.activeTypeId) {
        this.activeTypeId = "";
      } else {
        this.activeTypeId = e;
      }
      this.getDataList();
    },
    //点击切换风格分类
    areaBtn(e) {
      // this.activeAreaId = e;
      this.curPage = 1;
      this.caseDataList = [];
      if (e == this.activeAreaId) {
        this.activeAreaId = "";
      } else {
        this.activeAreaId = e;
      }
      this.getDataList();
    },

    //输入框事件
    inputValue(e) {
      console.log(e);
    },

    //获取列表
    getDataList() {
      this.$axios
        .get(`oe_queryAllCaseMt_.csp`, {
          params: {
            dbName: this.$dbName,
            userId: this.userId,
            curPage: this.curPage,
            style: this.styleActiveId == "-1" ? "" : this.styleActiveId,
            space: this.activeTypeId,
            colour: this.activeAreaId,
          },
        })
        .then((res) => {
          if (res.code == "1") {
            this.caseDataList = res.records;
            this.totalNum = res.count;
            // this.handleData(res.data[0].children);
            // this.typeDataList = res.data[0].children;
            // this.loading = false;
          }
        });
    },

    //获取案例分类
    getCaseTypeData() {
      this.$axios
        .get(`oe_queryCaseTypeMt_.csp`, {
          params: {
            dbName: this.$dbName,
          },
        })
        .then((res) => {
          if (res.code == "1") {
            res.data[0].children.forEach((item) => {
              console.log(item);
              if (item.title == "风格") {
                item.children.unshift({ id: "-1", title: "全部" });
                this.styleData = item.children;
                this.styleActiveId = item.children[0].id;
              } else if (item.title == "户型") {
                this.typeData = item.children;
              } else if (item.title == "面积") {
                this.areaData = item.children;
              }
            });
            // this.caseDataList = res.records;
            // this.totalNum = res.count;
            // this.handleData(res.data[0].children);
            // this.typeDataList = res.data[0].children;
            // this.loading = false;
          }
        });
    },

    //大屏兼容
    keyBnten() {
      document.addEventListener("keydown", (e) => {
        if (e.key === "Escape" || e.keyCode === 27) {
          window.top.postMessage("CALL_CSCREEN_GO_HOME", "*");
        }
      });
    },
  },

  //页面生命周期（进入加载）
  created() {
    this.getDataList();
    this.getCaseTypeData();
  },
};
</script>

<style  lang="scss" scoped>
.app {
  width: 100%;
  height: 100%;
  padding: 0 0.125rem 0 0.125rem;
  box-sizing: border-box;
  .top_box {
    width: 100%;
    height: 7vh;
    display: flex;
    align-items: center;
    align-items: flex-end;
    padding: 0 0.25rem 0.0625rem 0.25rem;
    box-sizing: border-box;

    .back_img {
      width: 0.3125rem;
      height: 0.3125rem;
      cursor: pointer;
    }
    .top_title {
      font-size: 0.2rem;
      margin-left: 0.0625rem;
      font-weight: bold;
    }
  }

  .content_box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .left_box {
      width: 1.1875rem;
      height: 89vh;
      background: #eef4fb;
      border-radius: 0.3125rem;
      padding: 0 0.0625rem 0.0625rem;
      box-sizing: border-box;
      .ul {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        overflow-y: auto;
        .li {
          list-style: none;
          font-size: 0.1563rem;
          color: #838383;
          height: 0.1875rem;
          cursor: pointer;
          padding: 0.25rem 0;
          margin-bottom: 0.2188rem;
        }
        .active_left {
          font-size: 0.1875rem;
          font-weight: bold;
          color: #000000;
          position: relative;
          ::after {
            content: "";
            width: 2.25rem;
            height: 0.25rem;
            border-radius: 0.25rem;
            background: #3782f4;
            position: absolute;
            left: 50%;
            bottom: 0.625rem;
            transform: translateX(-50%);
          }
        }
      }
    }
    .right_box {
      width: 90%;
      height: 89vh;
      // background: #eef4fb;
      .right_top {
        width: 100%;
        height: 1rem;
        background: #eef4fb;
        border-radius: 0.25rem;
        display: flex;
        align-items: center;
        padding: 0 0.125rem 0 0.125rem;
        box-sizing: border-box;
        .top_left {
          width: 82%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          .ul {
            display: flex;
            align-items: center;
            padding: 0.0938rem 0.125rem;
            overflow-x: auto;
            font-size: 0.1375rem;
            .li {
              list-style: none;
              padding-right: 0.25rem;
              margin-right: 0.125rem;
              cursor: pointer;
              color: #8a8a8a;
            }
            .active_type {
              color: black;
              font-weight: bold;
            }
            ::-webkit-scrollbar:vertical {
              width: 0.375rem;
            }
          }
        }
        .top_right {
          width: 18%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .input_box {
            width: 80%;
            height: 0.4375rem;
            position: relative;
            .input {
              width: 100%;
              height: 100%;
              background: #ffffff;
              border: none;
              border-radius: 2.875rem;
              font-size: 0.125rem;
              padding: 0 0.125rem 0 0.125rem;
              box-sizing: border-box;
            }
            .select_img {
              width: 0.1875rem;
              height: 0.1875rem;
              position: absolute;
              top: 50%;
              right: 0.125rem;
              transform: translateY(-50%);
              cursor: pointer;
            }
          }
        }
      }

      .product_list {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 0.1875rem;
        margin-top: 0.1875rem;
        .pro_boxs {
          width: 100%;
          cursor: pointer;
          .img_box {
            width: 100%;
            height: 2.3125rem;
            .cp_img {
              width: 100%;
              height: 100%;
            }
          }
          .cp_name {
            font-size: 0.1563rem;
            font-weight: bold;
            margin: 0.0938rem 0 0 0;
          }
          .cp_gge {
            font-size: 0.75rem;
            color: #202020;
          }
        }
      }

      .fenye_box {
        width: 100%;
        // height: 3.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 0.0625rem;
      }
    }
  }

  /* 修改垂直滚动条 */
  ::-webkit-scrollbar {
    width: 0.0625rem; /* 修改宽度 */
  }

  /* 修改滚动条轨道背景色 */
  ::-webkit-scrollbar-track {
  }

  /* 修改滚动条滑块颜色 */
  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
  }

  /* 修改滚动条滑块悬停时的颜色 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  /* 修改滚动条滑块移动时的颜色 */
  ::-webkit-scrollbar-thumb:active {
    // background-color: #333;
  }

  /* 修改滚动条滑块的圆角 */
  ::-webkit-scrollbar-thumb {
    border-radius: 0.375rem;
  }
}
</style>