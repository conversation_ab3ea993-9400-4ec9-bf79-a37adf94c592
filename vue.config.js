const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,
  // 基本路径
  publicPath: "./",
  outputDir: "yunping",
  assetsDir: "static",
  lintOnSave: true, // 是否开启eslint保存检测
  productionSourceMap: false, // 是否在构建生产包时生成sourcdeMap
  devServer: {
    client: {
      overlay: false,
    },
    proxy: {
      // '/api': {
      //   target: 'https://medodt.juesedao.com/',//目标地址
      //   ws: true,  //是否启用websockets
      //   changeOrigin: true, //开启代理：在本地会创建一个虚拟服务端，然后发送请求的数据，并同时接收请求的数据，这样服务端和服务端进行数据的交互就不会有跨域问题
      //   pathRewrite: { '^/api': '' }    //这里重写路径
      // },

      "/api": {
        target: "https://mdy.juesedao.com/",
        changeOrigin: true,
        ws: true,
        changeOrigin: true, //开启代理：在本地会创建一个虚拟服务端，然后发送请求的数据，并同时接收请求的数据，这样服务端和服务端进行数据的交互就不会有跨域问题
        pathRewrite: { '^/api': '' }    //这里重写路径
      },

    }
  },

})


