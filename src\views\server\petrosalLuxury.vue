<template>
  <div class="box">
    <div class="top_box">
      <img @click="goBack" class="jiantou_img" src="https://cdn.juesedao.cn/mdy/d0ccf550af4b44b99a3130636a26d23f" alt="">
      <div @click="goBack" class="title_box">岩奢服务</div>
      <div class="gang"></div>
    </div>
    <!-- <div class="quehuan_box">
      <div class="s_box">
        <div :class="activeNavId == '2' ? 'cs_box1' :'cs_box'" @click="toNav(2)">
          <img style="margin-right:0.125rem;" v-if="activeNavId != '2'" class="a_img" src="https://cdn.juesedao.cn/mdy/87124ea817284869a0c4e5d8aceceb3d" alt="">
          <p class="p1">平台优势</p>
        </div>
        <div :class="activeNavId == '1' ? 'cs_box1' :'cs_box'" @click="toNav(1)">
          <p class="p1">服务流程</p>
          <img style="margin-left:0.125rem;" v-if="activeNavId != '1'" class="a_img" src="https://cdn.juesedao.cn/mdy/97a7522daf184e2493f1daa1e58e1f50" alt="">
        </div>
      </div>
    </div> -->

    <div class="content_box" v-if="activeNavId == '2'">
      <div class="left_box">
        <el-collapse v-model="activeNames" @change="handleChange" accordion>
          <el-collapse-item title="品质无忧Worry-free quality" name="1">
            <div class="neirong_text">意大利原创设计与精制，官方独家发售，保证正品。Italian original design and refinement, official exclusive sale, guaranteed to be genuine</div>
          </el-collapse-item>
          <el-collapse-item title="配送无忧rry-free delivery" name="2">
            <div class="neirong_text">专业包装、专车配送与专人负责。Professional packaging, special car delivery and special person responsible</div>
          </el-collapse-item>
          <el-collapse-item title="设计无忧Worry-free design" name="3">
            <div class="neirong_text">官方设计团队专业测量，提供一站式设计咨询服务及解决方案。Offcial desian team professionally measures andprovides one-stop design consulting services andsolutions.</div>
          </el-collapse-item>
          <el-collapse-item title="安装无忧Worry-free installation" name="4">
            <div class="neirong_text">遴选具有8年以上专业技师提供上门安装服务。Select professional technicians with more than 8 yearsof experience to provide on-site installation services</div>
          </el-collapse-item>
          <el-collapse-item title="服务无忧Worry-free service" name="5">
            <div class="neirong_text">售前专业营销顾问贴心指引，10年质保畅享无忧售后。Professional pre-sales marketing consultants providethoughtful guidance, and enjoy worry-free after-sales,service
              with 10-year warranty.</div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="right_box">
        <img class="c_img_box" src="https://cdn.juesedao.cn/mdy/********************************" alt="">
      </div>
    </div>

    <div class="content_box1" v-if="activeNavId == '1'">
      <div class="left_box1">
        <img src="https://cdn.juesedao.cn/mdy/c9647a5bf46d4c5f9a1ae356b1f046be" alt="">
        <img src="https://cdn.juesedao.cn/mdy/67712987df494a5c9b2a0ecd90413acb" alt="">
        <img src="https://cdn.juesedao.cn/mdy/96f30acfb6374f4db4ea3e8f35874669" alt="">
      </div>
      <div class="right_box1">
        <div class="text_box1">
          <div class="c_text">OCUIYING service process</div>
          <div class="c_text_e">6 major service guarantee systems that protect customer rights and experiences in an all-round way, from pre-sales to after-sales, run through every transaction link to escort your home
            purchase journey
          </div>
          <div>
            <div class="ta_box">需求沟通demand communication</div>
            <div class="ta_box">产品选择Product selection</div>
            <div class="ta_box">产品选择Scheme design</div>
            <div class="ta_box">物流配送Logistics distribution</div>
            <div class="ta_box">维保服务Maintenance service</div>
            <div class="ta_box">安装验收installation acceptance</div>
          </div>
        </div>
      </div>
    </div>
    <backBtn></backBtn>
  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      activeNavId: "2",
    };
  },
  components: {
    backBtn,
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    toNav(e) {
      this.activeNavId = e;
    },
  },
};
</script>


<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  padding: 0.5rem 0.25rem 0 0.5rem;
  box-sizing: border-box;
  .top_box {
    width: 100%;
    display: flex;
    align-items: center;
    .jiantou_img {
      width: 0.2188rem;
      height: 0.2188rem;
      cursor: pointer;
    }
    .title_box {
      font-size: 0.2rem;
      color: #434343;
      margin: 0 0.1125rem;
      font-weight: 400;
      cursor: pointer;
    }
    .gang {
      width: 1px;
      height: 0.1875rem;
      background: #434343;
    }
  }
  .quehuan_box {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    .s_box {
      width: 3.75rem;
      height: 0.6875rem;
      background: #ededed;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 0.6875rem;
      padding: 0.075rem;
      box-sizing: border-box;
      .cs_box {
        width: 2rem;
        display: flex;
        align-items: center;
        height: 0.5625rem;
        justify-content: center;
        border-radius: 60px;
        .a_img {
          width: 0.2687rem;
          height: 0.2687rem;
        }
        .p1 {
          font-size: 0.2062rem;
          color: #a8a8a8;
        }
      }
      .cs_box1 {
        display: flex;
        width: 1.5938rem;
        align-items: center;
        justify-content: center;
        background: #000000;
        height: 0.5625rem;
        border-radius: 60px;
        .a_img {
          width: 0.2687rem;
          height: 0.2687rem;
        }
        .p1 {
          font-size: 0.2375rem;
          color: #fff;
          font-weight: bold;
        }
      }
    }
  }
  .content_box {
    width: 100%;
    display: flex;
    margin-top: 0.625rem;
    // align-items: center;
    justify-content: space-between;
    .left_box {
      width: 8.125rem;
      height: 3.75rem;
      // font-family: "aaa";
      /deep/ .el-collapse-item__header {
        font-size: 0.1563rem !important;
        text-transform: uppercase;
        height: 0.6875rem;
      }
      .neirong_text {
        font-size: 0.1163rem;
        color: #3d3d3d;
      }
    }
    .right_box {
      width: 6.875rem;
      .c_img_box {
        width: 100%;
      }
    }
  }
  .content_box1 {
    width: 100%;
    display: flex;
    margin-top: 0.375rem;
    // align-items: center;
    // justify-content: space-between;
    .left_box1 {
      width: 7.5rem;
      height: 5.5rem;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      overflow: hidden;
      img {
        width: 2.5rem;
        // height: 100%;
      }
    }
    .right_box1 {
      width: 6.875rem;
      padding: 0 0.5rem;
      box-sizing: border-box;
      margin-left: 0.25rem;
      .c_text {
        font-size: 0.2625rem;
        color: #070707;
        text-transform: uppercase;
      }
      .c_text_e {
        font-size: 0.1125rem;
        color: #3d3d3d;
        margin: 0.2rem 0 0.0625rem 0;
      }
      .ta_box {
        width: 100%;
        height: 0.6062rem;
        border-bottom: 1px solid #c8c8c8;
        display: flex;
        align-items: center;
        font-size: 0.1563rem;
        color: #1e1e1e;
        text-transform: uppercase;
        padding: 0.3625rem 0;
      }
      .ta_box:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>