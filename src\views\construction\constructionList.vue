<template>
  <div class="app" v-loading="loading">
    <Map v-show="activeId == '1'"></Map>
    <!-- <backBtn></backBtn> -->
    <div class="top_box" v-show="activeId == 2">
      <div class="left_box">
        <div class="title_box">
          <img @click="toBack" class="back_img" src="../../assets/images/back.png" alt="">
          <p class="p1">全部工地列表</p>
        </div>
        <div class="shaix_box">
          <div class="sx">
            <span class="sp1">工地城市</span>
            <img class="img2" src="../../assets/images/sx.png" alt="">
          </div>
          <div class="sx">
            <span class="sp1">工地区域</span>
            <img class="img2" src="../../assets/images/sx.png" alt="">
          </div>
          <div class="sx">
            <span class="sp1">施工阶段</span>
            <img class="img2" src="../../assets/images/sx.png" alt="">
          </div>
        </div>
      </div>

    </div>
    <div class="right_box">
      <div class="select_box">
        <p @click="activeBtn(1)" class="box_s" :class="activeId == '1' ? 'active_box' : ''">按地图区分</p>
        <p @click="activeBtn(2)" class="box_s" :class="activeId == '2' ? 'active_box' : ''">按列表区分</p>
      </div>
    </div>

    <div class="con_box" v-show="activeId == '2'">
      <div class="box_s1" v-for="(item,index) in gongdiList" :key="index" @click="toDetails(item.id)">
        <div class="st_img">
          <!-- <img class="img_aa" :src="item.coverPic" alt=""> -->
          <el-image class="img_aa" :src="item.coverPic" fit="cover"></el-image>
        </div>
        <div class="name1">{{item.city ? item.city + '|' : ''}}{{item.community}}</div>
        <div class="name2">{{item.houseType ? item.houseType + ' |' : ''}} {{item.houseArea ? item.houseArea + '平方m2 丨' : ''}} 施工中</div>
      </div>
    </div>

    <div class="fenye_box" v-show="activeId == '2'">
      <el-pagination background hide-on-single-page @current-change="currentChange" layout="prev, pager, next" :total="totalNum">
      </el-pagination>
    </div>

  </div>
</template>

<script>
import Map from "../../components/amap.vue";
// import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      activeId: "2",
      gongdiList: [],
      totalNum: "0",
      curPage: "1",
      city: "",
      area: "",
      orderStatus: "",
      keyword: "",
      loading: false,
    };
  },
  components: {
    Map,
    // backBtn,
  },
  methods: {
    toBack() {
      this.$router.go(-1);
    },
    activeBtn(e) {
      this.activeId = e;
    },

    //跳转详情
    toDetails(id) {
      this.$router.push({ path: "/constructionDetails", query: { id: id } });
    },

    //点击分页
    currentChange(e) {
      console.log(e);
      this.curPage = e;
      this.gongdiList = [];
      this.getGongdiList();
    },

    //获取工地列表
    getGongdiList() {
      let { curPage, city, area, orderStatus, keyword } = this;
      this.loading = true;
      this.$axios
        .get(`oe_workOnline_.csp`, {
          params: {
            dbName: this.$dbName,
            curPage,
            city,
            area,
            orderStatus,
            keyword,
          },
        })
        .then((res) => {
          this.loading = false;
          console.log(res);
          if (res.code == "1") {
            this.gongdiList = res.list;
            this.totalNum = res.totalNum;
          }
        });
    },
  },
  created() {
    this.activeId = this.$route.query.activeId;
    this.getGongdiList();
  },
};
</script>

<style lang="scss" scoped>
.app {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #fff 0%, #e4edfc 55%, #f0f5fb 100%);
  .top_box {
    width: 90%;
    // height: 5.625rem;
    display: flex;
    align-content: flex-end;
    justify-content: space-between;
    margin: 0 auto;
    padding-top: 0.125rem;
    // position: relative;

    .left_box {
      width: 70%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;

      .title_box {
        display: flex;
        align-items: center;
        .back_img {
          width: 0.375rem;
          height: 0.375rem;
          cursor: pointer;
        }
        .p1 {
          font-size: 0.25rem;
          color: #434343;
          margin-left: 0.125rem;
        }
      }
      .shaix_box {
        width: 100%;
        display: flex;
        align-items: center;
        margin-top: 0.25rem;
        .sx {
          height: 0.25rem;
          display: inline-block;
          background: black;
          border-radius: 1.875rem;
          padding: 0 0.125rem;
          margin-left: 0.125rem;
          line-height: 0.25rem;
          cursor: pointer;
          .sp1 {
            font-size: 0.125rem;
            color: white;
          }
          .img2 {
            width: 0.0625rem;
            height: 0.0625rem;
            margin-left: 0.125rem;
          }
        }
      }
    }
  }

  .right_box {
    width: 30%;
    //   height: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    position: absolute;
    right: 5%;
    top: 0.125rem;
    z-index: 99;
    .select_box {
      width: 3.5rem;
      height: 0.75rem;
      border-radius: 1.25rem;
      // border: 0.0063rem solid #d8dde4;
      box-sizing: border-box;
      box-shadow: 0px 0px 0.3438rem 0px rgba(0, 0, 0, 0.34);
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: white;
      .box_s {
        width: 50%;
        height: 100%;
        font-size: 0.1875rem;
        color: #737373;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      .active_box {
        background: #000;
        color: white;
        border-radius: 1.25rem;
        font-size: 0.1875rem;
        font-weight: bold;
      }
    }
  }

  .con_box {
    width: 90%;
    max-height: 79vh;
    margin: 0.125rem auto 0.125rem;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    overflow-y: auto;
    gap: 0.125rem;
    .box_s1 {
      width: 100%;
      cursor: pointer;
      //   height: 16.875rem;
      .st_img {
        width: 100%;
        // height: 100%;
        .img_aa {
          width: 100%;
          height: 33vh;
        }
      }
      .name1 {
        font-size: 0.1875rem;
        font-weight: bold;
        margin: 0.0625rem 0;
      }
      .name2 {
        font-size: 0.125rem;
        color: #212121;
      }
    }
  }
  .fenye_box {
    width: 100%;
    // height: 3.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  /* 自定义滚动条整体样式 */
  ::-webkit-scrollbar {
    width: 5px !important; /* 设置滚动条的宽度 */
    height: 0.375rem;
  }

  /* 自定义滚动条滑块样式 */
  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.27); /* 设置滑块的颜色 */
    border-radius: 0.3125rem;
  }

  /* 自定义滚动条轨道样式 */
  ::-webkit-scrollbar-track {
    background-color: #fff; /* 设置轨道的颜色 */
    border-radius: 0.3125rem;
  }

  .el-pagination .btn-prev,
  .el-pagination .btn-next {
    background-color: #f5f5f5; /* 举例：更改分页按钮的背景颜色 */
  }
}
</style>