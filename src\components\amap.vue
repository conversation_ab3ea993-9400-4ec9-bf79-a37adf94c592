<template>
  <div class="app">
    <div id="amapcontainer" style="width: 100vw; height: 100vh"></div>
    <div class="bottom_box">
      <div class="left" :class="activeId == '1' ? 'active_bg' : ''" @click="quehuanBtn(1)">
        <img class="img" src="https://cdn.juesedao.cn/mdy/d7887346ea3e4313bc95c8d0190b9141" alt="">
        <span>当前城市</span>
      </div>
      <div class="left" :class="activeId == '2' ? 'active_bg' : ''" @click="quehuanBtn(2)">
        <img class="img1" src="https://cdn.juesedao.cn/mdy/2b83db9bfd9043929626e76a520bc9e9" alt="">
        <span>全国城市</span>
      </div>
    </div>
  </div>
</template>
<script>
import AMapLoader from "@amap/amap-jsapi-loader";
// import location from "../../assets/positionLocation";
// import backBtn from "../../components/backBtn.vue";

window._AMapSecurityConfig = {
  securityJsCode: "e18de717bc0db5e1faa219d669d00e05", // '「申请的安全密钥」',
};
export default {
  data() {
    return {
      map: null,
      markerList: [],
      activeId: "2",
      zoom: "6",
      mapList: [
        // {
        //   name: "小王",
        //   address: "广东省广州市海珠区",
        //   lnglats: [113.312566, 23.085073],
        // },
        // {
        //   name: "小张",
        //   address: "广东省广州市黄埔区",
        //   lnglats: [113.480794, 23.177896],
        // },
        // {
        //   name: "小李",
        //   address: "广东省广州市荔湾区",
        //   lnglats: [113.220556, 23.10718],
        // },
        // {
        //   name: "小赵",
        //   address: "广东省广州市天河区",
        //   lnglats: [113.365438, 23.124231],
        // },
      ],
    };
  },
  //   components: {
  //     backBtn,
  //   },
  mounted() {
    this.getGongdiList();
    // DOM初始化完成进行地图初始化
    // this.initAMap();
  },
  methods: {
    //切换地区
    quehuanBtn(e) {
      this.activeId = e;
      if (e == "1") {
        // this.getLoadtion();
      } else {
        this.zoom = "16";
        console.log(this.zoom);
        this.initAMap();
      }
    },

    //获取工地数据
    getGongdiList() {
      // this.loading = true;
      this.$axios
        .get(`oe_getProjects_.csp`, {
          params: {
            dbName: this.$dbName,
          },
        })
        .then((res) => {
          // this.loading = false;
          console.log(res);
          if (res.code == "1") {
            res.records.forEach((item) => {
              let arr = [item.storeLongitude];
              arr.push(item.storeLatitude);
              item.lnglats = arr;
              item.name = item.community;
            });
            this.mapList = res.records;
            // setTimeout(() => {
            this.initAMap();
            // }, 2000);
            // this.totalNum = res.totalNum;
          }
        });
    },

    logMapinfo() {},

    //获取当前位置
    getLoadtion() {
      //   console.log("111");
      //   let _that = this;
      //   let geolocation = location.initMap("map-container"); // 定位
      //   console.log(geolocation);
      //   AMap.event.addListener(geolocation, "complete", (result) => {
      //     // _that.lat = result.position.lat;
      //     // _that.lng = result.position.lng;
      //     // _that.location = result.formattedAddress;
      //   });
    },

    initAMap() {
      AMapLoader.reset();
      AMapLoader.load({
        key: "e18de717bc0db5e1faa219d669d00e05", // 申请好的Web端开发者Key，首次调用 load 时必填
        // version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: [
          "AMap.Scale",
          "AMap.ToolBar",
          "AMap.ControlBar",
          "AMap.Geocoder",
          "AMap.Marker",
          "AMap.CitySearch",
          "AMap.Geolocation",
          "AMap.AutoComplete",
          "AMap.InfoWindow",
        ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      })
        .then((AMap) => {
          // 获取到作为地图容器的DOM元素，创建地图实例
          this.map = new AMap.Map("amapcontainer", {
            //设置地图容器id
            // resizeEnable: true,
            zoom: "8", // 地图显示的缩放级别
            viewMode: "3D", // 使用3D视图
            zoomEnable: true, // 地图是否可缩放，默认值为true
            dragEnable: true, // 地图是否可通过鼠标拖拽平移，默认为true
            doubleClickZoom: true, // 地图是否可通过双击鼠标放大地图，默认为true
            // zoom: this.zoom, //初始化地图级别
            // center: [113.370824, 23.131265], // 初始化中心点坐标 广州
            // mapStyle: "amap://styles/darkblue", // 设置颜色底层
            content: '<div style="width:200px;">这里是点位信息</div>',
          });
          // 在图面添加工具条控件, 工具条控件只有缩放功能
          this.map.addControl(
            new AMap.ToolBar({
              liteStyle: true,
            })
          );
          //获取并展示当前城市信息

          this.setMapMarker();
        })
        .catch((e) => {
          console.log(e);
        });
    },
    // 增加点标记
    setMapMarker() {
      let that = this;
      // 创建 AMap.Icon 实例
      let icon = new AMap.Icon({
        size: new AMap.Size(36, 36), // 图标尺寸
        image: "https://cdn.juesedao.cn/mdy/a932378e1bc140ab94ff5dab436cc12c", // Icon的图像
        imageSize: new AMap.Size(28, 28), // 根据所设置的大小拉伸或压缩图片
        imageOffset: new AMap.Pixel(0, 0), // 图像相对展示区域的偏移量，适于雪碧图等
      });
      let makerList = [];
      this.mapList.forEach((item) => {
        // 遍历生成多个标记点
        let marker = new AMap.Marker({
          map: this.map,
          zIndex: 9999999,
          icon: icon, // 添加 Icon 实例
          offset: new AMap.Pixel(-13, -30), //icon中心点的偏移量
          position: item.lnglats, // 经纬度对象new AMap.LngLat(x, y)，也可以是经纬度构成的一维数组[116.39, 39.9]
        });
        marker.setMap(that.map);
        // 设置label标签
        // label默认蓝框白底左上角显示，样式className为：amap-marker-label
        marker.setLabel({
          style: {
            // 自定义样式
            "background-color": "red", // 背景颜色
            color: "yellow", // 文字颜色
            border: "2px solid red !important", // 边框
          },
          // 修改label相对于maker的位置
          offset: new AMap.Pixel(-130, -90), //点标记定位
          //   content: `<div style='width:283px;height:60px;background:#FE8902;border-radius:50px 50px 50px 0; cursor: pointer;  display: flex; align-content: center;'>
          //   <div style="width:65px; height:100%; display: flex;align-items: center; justify-content: center; border: 1px solid white;  box-sizing: border-box;">
          //       <img style="width:90%; heigth:100%;" src="https://cdn.juesedao.cn/mdy/f9a33ac417fd476cbaba0cbd860ff02a" alt="">
          //     </div>
          //   <div style="widht:215px; height:100%; display: flex;align-items: center; padding: 0 10px; box-sizing: border-box; font-size: 16px; font-weight: 600; color: white;">${item.name}</div>
          // </div> `,
        });
        makerList.push(marker);
      });

      this.map.add(makerList);
      // 自动适应显示想显示的范围区域
      this.map.setFitView();
    },
  },
};
</script>
<style lang="scss" scoped>
.app {
  width: 100%;
  height: 100%;

  .bottom_box {
    width: 4.375rem;
    height: 0.875rem;
    position: fixed;
    left: 50%;
    bottom: 0.625rem;
    transform: translateX(-50%);
    background: #cacaca;
    border-radius: 2.5rem;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    overflow: hidden;

    .left {
      width: 100%;
      height: 100%;
      cursor: pointer;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.1875rem;
      .img {
        width: 0.25rem;
        margin-right: 0.1875rem;
      }
      .img1 {
        width: 0.3125rem;
        margin-right: 0.1875rem;
      }
    }
    .active_bg {
      background-color: black;
      color: white;
    }
  }
  .amap-marker-label {
    padding: 2px 6px;
    border-radius: 5px;
    box-shadow: 0 2px 6px 0 rgba(114, 124, 245, 0.5);
    background: rgba(0, 0, 0, 0) !important;
    text-align: center;
    font-size: 14px;
    border: 0.0625rem solid #fff !important;
  }
  .amap-marker-label {
    position: absolute;
    z-index: 2;
    border: 1px solid #ccc !important;
    background-color: white;
    white-space: nowrap;
    cursor: default;
    padding: 3px;
    font-size: 12px;
    line-height: 14px;
  }
}
</style>
