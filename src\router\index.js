import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'home',
    component: () => import('../views/home/<USER>')
  },
  {
    path: '/productList',
    name: 'productList',
    component: () => import('../views/product/product.vue'),

  },
  {
    path: '/caseList',
    name: 'caseList',
    component: () => import('../views/case/case.vue')
  },
  {
    path: '/productDetails',
    name: 'productDetails',
    component: () => import('../views/product/productDetails.vue')
  },
  {
    path: '/relatedCases',
    name: 'relatedCases',
    component: () => import('../views/case/relatedCases.vue')
  },
  {
    path: '/relatedConstructionSites',
    name: 'relatedConstructionSites',
    component: () => import('../views/construction/relatedConstructionSites.vue')
  },
  {
    path: '/serverList',
    name: 'serverList',
    component: () => import('../views/server/serverLisr.vue')
  },
  {
    path: '/constructionList',
    name: 'constructionList',
    component: () => import('../views/construction/constructionList.vue')
  },
  {
    path: '/constructionDetails',
    name: 'constructionDetails',
    component: () => import('../views/construction/constructionDetails.vue')
  },
  {
    path: '/map',
    name: 'map',
    component: () => import('../views/map/map.vue')
  },
  {
    path: '/caseDetails',
    name: 'caseDetails',
    component: () => import('../views/case/caseDetails.vue')
  },
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export default router
