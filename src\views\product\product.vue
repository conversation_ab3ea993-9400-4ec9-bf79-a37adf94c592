<template>
  <div class="app" v-loading="loading">
    <!-- <backBtn></backBtn> -->
    <div class="top_box">
      <img @click="goBack" class="back_img" src="../../assets/images/back.png" alt="">
      <p class="top_title">产品列表</p>
    </div>
    <div class="content_box">
      <div class="left_box">
        <ul class="ul">
          <li class="li " :class="activeXilieId == item.id ? 'active_left' : ''" v-for="(item,index) in seriesDataList" :key="index" @click="xilBtn(item.id)">{{item.title}}</li>
          <!-- <li class="li">全部</li> -->
        </ul>
      </div>
      <div class="right_box">
        <div class="right_top">
          <div class="top_left">
            <ul class="ul">
              <li class="li" :class="item.id == guigeId ? 'active_a' : ''" v-for="(item,index) in specificationDataList" :key="index" @click="guigeBtn(item.id)">{{item.title}}</li>
            </ul>
            <ul class="ul">
              <li class="li" :class="item.id == colorId ? 'active_a' : ''" v-for="(item,index) in colorDataList" :key="index" @click="colorBtn(item.id)">{{item.title}}</li>
            </ul>
          </div>
          <div class="top_right">
            <div class="input_box">
              <input type="text" class="input" placeholder="搜索产品信息">
              <img class="select_img" src="../../assets/images/select.png" alt="">
            </div>
          </div>
        </div>

        <div class="product_list">
          <div class="pro_boxs" v-for="(item,index) in productList" :key="index" @click="toDetalis(item.id)">
            <div class="img_box">
              <!-- <img class="cp_img" :src="item.productUrl" alt=""> -->
              <el-image class="cp_img" :src="item.productImage ? item.productImage : item.productUrl" fit="cover"></el-image>
            </div>
            <div class="cp_name">{{item.name}}</div>
            <div class="cp_gge">{{item.productModel}}</div>
          </div>
        </div>

        <div class="fenye_box">
          <el-pagination background hide-on-single-page layout="prev, pager, next" :total="totalNum">
          </el-pagination>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
// import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {
      curPage: 1,
      productList: [], //产品列表
      totalNum: "0",
      seriesDataList: [],
      colorDataList: [],
      specificationDataList: [],

      activeXilieId: "",
      guigeId: "",
      colorId: "",
      loading: false,
    };
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    components: {
      // backBtn,
    },
    //选中系列数据
    xilBtn(e) {
      this.activeXilieId = e;
      this.curPage = "1";
      this.productList = [];
      this.getDataList();
    },

    //规格筛选
    guigeBtn(e) {
      this.productList = [];
      this.totalNum = "";
      if (this.guigeId == e) {
        this.guigeId = "";
      } else {
        this.guigeId = e;
      }
      this.getDataList();
    },

    //色彩筛选
    colorBtn(e) {
      this.productList = [];
      this.totalNum = "";
      if (this.colorId == e) {
        this.colorId = "";
      } else {
        this.colorId = e;
      }
      this.getDataList();
    },

    //获取列表
    getDataList() {
      this.loading = true;
      this.$axios
        .get(`oe_productScreenMt_.csp`, {
          params: {
            dbName: this.$dbName,
            // userId: this.userId,
            curPage: this.curPage,
            series: this.activeXilieId == "-1" ? "" : this.activeXilieId,
            size: this.guigeId,
            colour: this.colorId,
            // style: this.styleActiveId == "-1" ? "" : this.styleActiveId,
            // space: this.activeTypeId,
            // colour: this.activeAreaId,
          },
        })
        .then((res) => {
          this.loading = false;
          console.log(res);
          if (res.code == "1") {
            this.productList = res.records;
            this.totalNum = res.count;
            // this.handleData(res.data[0].children);
            // this.typeDataList = res.data[0].children;
            // this.loading = false;
          }
        });
    },

    //获取列表
    getProdectType() {
      this.$axios
        .get(`oe_queryProductTypeIt_.csp`, {
          params: {
            dbName: this.$dbName,
          },
        })
        .then((res) => {
          console.log(res);
          if (res.code == "1") {
            res.data[0].children.forEach((item) => {
              console.log(item);
              if (item.title == "系列") {
                item.children.unshift({ id: "-1", title: "全部" });
                this.seriesDataList = item.children;
                this.activeXilieId = item.children[0].id;
              } else if (item.title == "色彩") {
                this.colorDataList = item.children;
              } else if (item.title == "规格") {
                this.specificationDataList = item.children;
              }
            });
          }
        });
    },

    toDetalis(id) {
      this.$router.push({ path: "/productDetails", query: { id: id } });
    },
  },
  //页面生命周期（进入加载）
  created() {
    this.getDataList();
    this.getProdectType();
  },
};
</script>

<style  lang="scss" scoped>
.app {
  width: 100%;
  height: 100%;
  padding: 0 0.25rem 0 0.125rem;
  box-sizing: border-box;
  .top_box {
    width: 100%;
    height: 7vh;
    display: flex;
    align-items: flex-end;
    padding: 0 0.25rem 0.125rem 0.375rem;
    box-sizing: border-box;

    .back_img {
      width: 0.3125rem;
      height: 0.3125rem;
      cursor: pointer;
    }
    .top_title {
      font-size: 0.25rem;
      margin-left: 0.125rem;
    }
  }

  .content_box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .left_box {
      width: 1.25rem;
      height: 89vh;
      background: #eef4fb;
      border-radius: 0.25rem;
      padding: 0.25rem 0.0125rem 0.125rem;
      box-sizing: border-box;
      overflow: hidden;
      .ul {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        overflow-y: auto;
        .li {
          list-style: none;
          font-size: 0.1563rem;
          color: #838383;
          height: 3.125rem;
          cursor: pointer;
          padding-bottom: 20px;
          margin-bottom: 0.25rem;
        }
        .active_left {
          font-size: 0.1875rem;
          font-weight: bold;
          color: #000000;
          position: relative;
          ::after {
            content: "";
            width: 2.25rem;
            height: 0.25rem;
            border-radius: 0.25rem;
            background: #3782f4;
            position: absolute;
            left: 50%;
            bottom: 0.625rem;
            transform: translateX(-50%);
          }
        }
      }
    }
    .right_box {
      width: 89%;
      height: 89vh;
      // background: #eef4fb;
      .right_top {
        width: 100%;
        height: 1rem;
        background: #eef4fb;
        border-radius: 0.25rem;
        display: flex;
        align-items: center;
        padding: 0 0.25rem 0 0.125rem;
        box-sizing: border-box;
        .top_left {
          width: 82%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          .ul {
            display: flex;
            align-items: center;
            padding: 0.0625rem 0.125rem;
            overflow-x: auto;
            box-sizing: border-box;
            .li {
              list-style: none;
              padding-right: 0.25rem;
              margin-right: 0.1875rem;
              cursor: pointer;
              color: #8a8a8a;
              font-size: 0.125rem;
            }
            .active_a {
              font-weight: bold;
              color: black;
            }
            ::-webkit-scrollbar:vertical {
              width: 0.375rem;
            }
          }
        }
        .top_right {
          width: 18%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .input_box {
            width: 100%;
            height: 0.4375rem;
            display: flex;
            align-content: center;
            justify-content: flex-end;
            position: relative;
            .input {
              width: 2.125rem;
              height: 100%;
              background: #ffffff;
              border: none;
              border-radius: 2.875rem;
              font-size: 0.1275rem;
              padding: 0 0.4375rem 0 0.1875rem;
              box-sizing: border-box;
            }
            .select_img {
              width: 0.1875rem;
              height: 0.1875rem;
              position: absolute;
              top: 50%;
              right: 0.1875rem;
              transform: translateY(-50%);
              cursor: pointer;
            }
          }
        }
      }

      .product_list {
        width: 100%;
        height: 81%;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 0.1875rem;
        margin-top: 0.125rem;
        overflow-y: auto;
        .pro_boxs {
          width: 100%;
          cursor: pointer;
          .img_box {
            width: 100%;
            height: 1.875rem;
            border-radius: 0.0625rem;
            overflow: hidden;
            .cp_img {
              width: 100%;
              height: 100%;
            }
          }
          .cp_name {
            font-size: 0.1875rem;
            font-weight: bold;
            margin: 0.0625rem 0 0.0625rem 0;
          }
          .cp_gge {
            font-size: 0.125rem;
            color: #202020;
          }
        }
      }

      .fenye_box {
        width: 100%;
        // height: 3.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 0.0625rem;
      }
    }
  }

  /* 修改垂直滚动条 */
  ::-webkit-scrollbar {
    width: 0.0625rem; /* 修改宽度 */
  }

  /* 修改滚动条轨道背景色 */
  ::-webkit-scrollbar-track {
  }

  /* 修改滚动条滑块颜色 */
  ::-webkit-scrollbar-thumb {
    // background-color: #888;
    background-color: rgba(0, 0, 0, 0.1);
  }

  /* 修改滚动条滑块悬停时的颜色 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  /* 修改滚动条滑块移动时的颜色 */
  ::-webkit-scrollbar-thumb:active {
    background-color: #333;
  }

  /* 修改滚动条滑块的圆角 */
  ::-webkit-scrollbar-thumb {
    border-radius: 0.375rem;
  }
  //水平方向滚动条
  ::-webkit-scrollbar:horizontal {
    height: 0.0625rem;
  }
}
</style>