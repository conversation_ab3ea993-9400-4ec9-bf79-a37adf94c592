<template>
  <div class="app">
    <backBtn></backBtn>
    <div class="top_box">
      <img class="img" @click="toBack()" src="../../assets/images/back.png" alt="">
      <p class="top_title">案例详情</p>
    </div>

    <div class="con_box1">
      <div class="left_box1">
        <div class="title_a">云府空间设计</div>
        <div class="info_box">
          <div class="info" v-for="(item,index) in 3" :key="index">
            <img class="info_img" src="../../assets/images/Location_pin.png" alt="">
            <p class="info_text">江西省赣州市龙南市</p>
          </div>
        </div>
        <div class="jianjie_box">
          <div class="jianjie_title">案例简介</div>
          <div class="nr_box">
            本案例采用的是现代简约风格，采用多种现代流行个性色调融入空间中，让空间的现代感十足，也保证了每个功能区的独立性!
          </div>
        </div>
      </div>
      <div class="right_box1">
        <div class="img_box2">
          <img class="img_s" src="https://cdn.juesedao.cn/mdy/16d09ad16d31427ab03c84bf5a008a6f" alt="">
        </div>
      </div>
    </div>

    <div class="con_box2">

    </div>
  </div>
</template>

<script>
import backBtn from "../../components/backBtn.vue";
export default {
  data() {
    return {};
  },
  components: {
    backBtn,
  },
  methods: {
    toBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.app {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #fff 0%, #e4edfc 55%, #f0f5fb 100%);
  .ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .top_box {
    width: 95%;
    display: flex;
    align-items: center;
    padding: 2.5rem 0 1.25rem;
    margin: 0 auto;
    .img {
      width: 2rem;
      height: 2rem;
      cursor: pointer;
      margin-right: 0.625rem;
    }
    .top_title {
      font-size: 1.4375rem;
    }
  }

  .con_box1 {
    width: 95%;
    height: 40.625rem;
    margin: 0 auto;
    display: flex;
    .left_box1 {
      width: 32%;
      height: 100%;
      padding: 1.25rem 1.875rem 0.625rem 0.625rem;
      box-sizing: border-box;
      .title_a {
        font-size: 2.125rem;
        font-weight: bold;
      }
      .info_box {
        width: 100%;
        display: flex;
        align-items: center;
        margin-top: 1.875rem;
        .info {
          width: 33%;
          display: flex;
          align-items: center;
          .info_img {
            width: 1.75rem;
            height: 1.75rem;
          }
          .info_text {
            font-size: 0.875rem;
          }
        }
      }
      .jianjie_box {
        width: 100%;
        margin: 1.875rem 0;
        .jianjie_title {
          font-size: 1.375rem;
        }
        .nr_box {
          font-size: 1rem;
          color: black;
          margin-top: 1.875rem;
        }
      }
    }

    .right_box1 {
      width: 67%;
      height: 100%;
      overflow: hidden;
      .img_box2 {
        width: 100%;
        .img_s {
          width: 100%;
        }
      }
    }
  }

  .con_box2 {
    width: 100%;
    height: 34.4375rem;
    background: white;
  }

  /* 自定义滚动条整体样式 */
  ::-webkit-scrollbar {
    width: 5px !important; /* 设置滚动条的宽度 */
    height: 0.375rem;
  }

  /* 自定义滚动条滑块样式 */
  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.27); /* 设置滑块的颜色 */
    border-radius: 0.3125rem;
  }

  /* 自定义滚动条轨道样式 */
  ::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, 0.67); /* 设置轨道的颜色 */
    border-radius: 0.3125rem;
  }
}
</style>
