<template>
  <div class="app" v-loading="loading">
    <div class="left_box">
      <div class="top_back">
        <img @click="toBack" class="back_img" src="../../assets/images/back.png" alt="">
        <p class="p1">产品详情</p>
      </div>
      <div class="zhong_box">
        <div class="atout_box">
          <div class="boxs_1">
            <img class="icon1" src="../../assets/images/Apps.png" alt="">
            <p class="title_a">{{productDetails.spacename}}</p>
          </div>
          <div class="boxs_1">
            <img class="icon1" src="../../assets/images/Component.png" alt="">
            <p class="title_a">{{productDetails.sizename}}</p>
          </div>
          <div class="boxs_1">
            <img class="icon1" src="../../assets/images/Playlist.png" alt="">
            <p class="title_a">{{productDetails.seriesname}}</p>
          </div>
          <div class="boxs_1">
            <img class="icon1" src="../../assets/images/yuan.png" alt="">
            <p class="title_a">{{productDetails.colourname}}</p>
          </div>
        </div>
        <div class="product_list">
          <div class="product_boxs" v-for="(item,index) in 5" :key="index">
            <div class="cp_img">
              <img class="imgs" src="https://cdn.juesedao.cn/mdy/6bcc5847295a496bab9e0719086d592a" alt="">
            </div>
            <div class="cp_name">SH1530060GB114</div>
          </div>
        </div>
      </div>
      <div class="name_a">
        <p>{{productDetails.name}}</p>
      </div>
      <div class="guige">
        <span class="span1">{{productDetails.productModel}}</span>
      </div>
    </div>
    <div class="right_box">
      <!-- <img class="right_img" src="https://cdn.juesedao.cn/mdy/74a7e2b94a074949805a5ac254ad1e17" alt=""> -->
      <el-carousel :interval="5000" height="100%" style=" height:100%; " arrow="always">
        <el-carousel-item style="width:100%; height:100%; " v-for="(item,index) in productDetails.productImages" :key="index">
          <el-image style="width: 100%; height: 100%" :src="item.url" fit="cover">
          </el-image>
        </el-carousel-item>
      </el-carousel>
      <!-- <el-carousel :interval="5000" height="100%" arrow="always" @change="changePage">
        <el-carousel-item v-for="(item, index) in productDetails.productImages" :key="index" ref="img">
          <el-image style="width: 100%; height: 100%" :src="item.url" fit="contain">
          </el-image>
        </el-carousel-item>
      </el-carousel> -->
      <!-- 二维码 -->
      <div class="qr_img">
        <img class="qr" :src="productDetails.qrImage" alt="">
      </div>
      <!-- 相关功能 -->
      <div class="fun_boxs">
        <div class="f_boxs" @click="toFunBtn(1)">
          <img class="f_img" src="../../assets/images/720.png" alt="">
          <p class="title">720°全景</p>
        </div>
        <div class="f_boxs" @click="toFunBtn(2)">
          <img class="f_img1" src="../../assets/images/yuan1.png" alt="">
          <p class="title">相关案例</p>
        </div>
        <div class="f_boxs" @click="toFunBtn(3)">
          <img class="f_img2" src="../../assets/images/gongdi.png" alt="">
          <p class="title">相关工地</p>
        </div>
      </div>
      <!-- 返回按钮 -->
      <div class="back_box" @click="toBack">
        <img class="back_img1" src="../../assets/images/back_icon.png" alt="">
        <p class="t_name">返回</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      productId: "",
      curPage: 1,
      productDetails: "",
      loading: false,
      srcList: [],
    };
  },
  methods: {
    toBack() {
      this.$router.go(-1);
    },

    //跳转功能按钮
    toFunBtn(e) {
      if (e == "1") {
        //720

        return false;
      } else if (e == "2") {
        //相关案例
        this.$router.push({ path: "/relatedCases" });
        return false;
      } else {
        this.$router.push({ path: "/relatedConstructionSites" });
        //相关工地
      }
    },

    //获取列表
    getDataList() {
      this.loading = true;
      this.$axios
        .get(`oe_queryproductItemMt_.csp`, {
          params: {
            dbName: this.$dbName,
            curPage: this.curPage,
            id: this.productId,
          },
        })
        .then((res) => {
          this.loading = false;
          if (res.code == "1") {
            if (this.isJson(res.records[0].productGuige)) {
              res.records[0].productGuige = JSON.parse(
                res.records[0].productGuige
              );
            }
            res.records[0].productGuige.forEach((item) => {
              if (this.isJson(item.images)) {
                item.images = JSON.parse(item.images);
              }
            });
            if (this.isJson(res.records[0].productImages)) {
              res.records[0].productImages = JSON.parse(
                res.records[0].productImages
              );
            }

            this.productDetails = res.records[0];
            console.log(this.productDetails);
          }
        });
    },

    //判断是否为json类型

    isJson(item) {
      if (typeof item !== "string") {
        return false;
      }
      try {
        JSON.parse(item);
        return true;
      } catch (e) {
        return false;
      }
    },
  },
  created() {
    this.productId = this.$route.query.id;
    this.getDataList();
  },
};
</script>

<style lang="scss" scoped>
.app {
  width: 100%;
  display: flex;
  .left_box {
    width: 26%;
    height: 100vh;
    background-image: linear-gradient(#fff 10%, #f0f5fb 100%);
    padding: 0.125rem 0;
    box-sizing: border-box;
    .top_back {
      width: 100%;
      padding: 0 0.375rem;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      .back_img {
        width: 0.3125rem;
        height: 0.3125rem;
        cursor: pointer;
      }
      .p1 {
        font-size: 0.1875rem;
        margin-left: 0.125rem;
        font-weight: bold;
      }
    }

    .zhong_box {
      width: 96%;
      //   height: 18.75rem;
      background: white;
      border-radius: 0 0.375rem 0.375rem 0;
      padding: 0.1875rem 0.25rem 0.1875rem 0.375rem;
      box-sizing: border-box;
      margin: 0.125rem 0;
      .atout_box {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        .boxs_1 {
          width: 50%;
          padding-right: 0.25rem;
          box-sizing: border-box;
          margin: 0.125rem 0 0.25rem;
          .icon1 {
            width: 0.25rem;
            height: 0.25rem;
          }
          .title_a {
            font-size: 0.125rem;
            color: #2b2b2b;
            margin-top: 0.0625rem;
          }
        }
      }
      .product_list {
        width: 100%;
        height: 2.9375rem;
        display: flex;
        overflow-x: auto;
        .product_boxs {
          width: 8.75rem;
          height: 100%;
          margin-right: 0.0625rem;

          .cp_name {
            font-size: 0.125rem;
            color: #171717;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .cp_img {
            width: 100%;
            height: 2.625rem;
            .imgs {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
    .name_a {
      font-size: 0.25rem;
      color: #262626;
      font-weight: bold;
      padding: 0 0.375rem;
      box-sizing: border-box;
      margin-top: 0.1875rem;
    }
    .guige {
      width: 100%;
      padding: 0 0.375rem;
      box-sizing: border-box;
      margin-top: 0.1875rem;
      .span1 {
        padding: 0.0625rem 0.1875rem;
        background: rgba(0, 0, 0, 0.27);
        font-size: 0.125rem;
        color: white;
        border-radius: 1.25rem;
      }
    }

    /* 自定义滚动条整体样式 */
    ::-webkit-scrollbar {
      width: 5px !important; /* 设置滚动条的宽度 */
      height: 0.0625rem;
    }

    /* 自定义滚动条滑块样式 */
    ::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.27); /* 设置滑块的颜色 */
      border-radius: 0.3125rem;
    }

    /* 自定义滚动条轨道样式 */
    ::-webkit-scrollbar-track {
      background-color: #fff; /* 设置轨道的颜色 */
      border-radius: 0.3125rem;
    }
  }

  .right_box {
    width: 74%;
    height: 100vh;
    overflow: hidden;
    position: relative;
    .el-carousel__item h3 {
      color: #475669;
      font-size: 18px;
      opacity: 0.75;
      // line-height: 300px;
      margin: 0;
    }

    .right_img {
      width: 100%;
      height: 100%;
    }
    .qr_img {
      width: 1.25rem;
      height: 1.25rem;
      border-radius: 11.375rem;
      overflow: hidden;
      position: absolute;
      top: 0.1875rem;
      right: 0.1875rem;
      z-index: 99;
      .qr {
        width: 100%;
        height: 100%;
      }
    }
    .fun_boxs {
      width: 0.875rem;
      height: 3.125rem;
      background: rgba(0, 0, 0, 0.34);
      position: absolute;
      top: 2.125rem;
      right: 0.25rem;
      border-radius: 1.875rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;
      padding: 0.1875rem 0;
      box-sizing: border-box;
      z-index: 99999;
      .f_boxs {
        // width: ;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .f_img {
          width: 0.375rem;
          height: 0.375rem;
        }
        .f_img1 {
          width: 0.3125rem;
          height: 0.3125rem;
        }
        .f_img2 {
          width: 0.3125rem;
          height: 0.3125rem;
        }
        .title {
          font-size: 0.125rem;
          color: white;
          margin-top: 0.0625rem;
        }
      }
    }

    // 返回
    .back_box {
      // width: 1.875rem;
      // height: 4.5625rem;
      background: rgba(0, 0, 0, 0.44);
      position: absolute;
      right: 0;
      top: 6.25rem;
      color: white;
      border-radius: 6.25rem 0 0 6.25rem;
      display: flex;
      align-items: center;
      padding: 0.125rem 0.25rem 0.125rem 0.125rem;
      z-index: 99999;
      cursor: pointer;
      .back_img1 {
        width: 0.375rem;
        height: 0.375rem;
      }
      .t_name {
        font-size: 0.125rem;
        margin-left: 0.125rem;
      }
    }
  }
}
</style>

